import { useMutation } from '@tanstack/react-query'

import { restPatch } from 'src/api/rest-api-caller'
import { enqueueSnackbarWithCloseAction } from 'src/components/Snackbar/Notistack/utils'

import { ctIntl } from 'cartrack-ui-kit'
import type { ScdfApproveBooking } from './types'
import useScdfBookingMutationInvalidation from './useScdfBookingMutationInvalidation'
import { createApproveRequestPayload } from './utils'

function approveBooking(
  params: ScdfApproveBooking.Params,
): Promise<ScdfApproveBooking.Response> {
  const requestPayload: ScdfApproveBooking.RequestPayload =
    createApproveRequestPayload(params)

  return restPatch<ScdfApproveBooking.Response>(
    `/scdf/booking/${params.bookingId}/approve`,
    requestPayload,
  )
}

const useApproveBookingMutation = () => {
  const invalidateBookingQueries = useScdfBookingMutationInvalidation()

  return useMutation({
    mutationFn: approveBooking,
    onSuccess() {
      enqueueSnackbarWithCloseAction(
        ctIntl.formatMessage({ id: 'Approve booking successfully.' }),
        { variant: 'success' },
      )
    },
    onSettled: (_, __, variables) => {
      invalidateBookingQueries({
        journeys: variables.journeys,
        shouldInvalidateSpecificBooking: true,
        bookingId: variables.bookingId,
      })
    },
    onError: (error) => {
      enqueueSnackbarWithCloseAction(error.message, { variant: 'error' })
    },
  })
}

export default useApproveBookingMutation

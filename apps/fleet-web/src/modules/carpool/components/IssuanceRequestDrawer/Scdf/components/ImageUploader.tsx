import { useCallback, useState } from 'react'
import {
  Box,
  Button,
  CircularProgress,
  IconButton,
  styled as Mu<PERSON><PERSON>ty<PERSON>,
  Stack,
  Tooltip,
  Typography,
} from '@karoo-ui/core'
import CloseIcon from '@mui/icons-material/Close'
import InsertDriveFileIcon from '@mui/icons-material/InsertDriveFile'
import UploadFileIcon from '@mui/icons-material/UploadFile'
import VisibilityIcon from '@mui/icons-material/Visibility'
import {
  useDropzone,
  type Accept,
  type FileError,
  type FileRejection,
  type FileWithPath,
} from 'react-dropzone'

import { enqueueSnackbarWithCloseAction } from 'src/components/Snackbar/Notistack/utils'
import { showSupportedFileFormats } from 'src/util-functions/file-utils'

import { namespacedUUIDv4 } from 'cartrack-utils'
import ImageModal from '../../../ImageModal'
import {
  useDeleteImageMutation,
  useUploadImageMutation,
} from '../api/useImageUploadMutations'
import type { ExistingAttachmentData } from '../types'
import { createFilePreviewUrl, fileToBase64, revokeFilePreviewUrl } from '../utils'

const BYTES_PER_KILOBYTE = 1024
const FILE_UPLOAD_MAX_SIZE = 3 * BYTES_PER_KILOBYTE * BYTES_PER_KILOBYTE // 3MB
const FILE_NAME_MAX_SIZE = 65

export type UploadedImageData = {
  guid: string
  extension: string
  fileName: string
  contentType: string
  previewUrl: string
}

export type UploadingFile = {
  file: File
  id: string
  progress: number
}

export type ImageUploaderProps = {
  uploadedImages: Array<UploadedImageData>
  onBatchImagesAdd: (images: Array<UploadedImageData>) => void
  onImageRemove: (guid: string) => void
  existingAttachments: Array<ExistingAttachmentData>
  onExistingAttachmentRemove: (attachmentId: number) => void
  maxFiles?: number
  disabled: boolean
}

const imageFileTypes: Accept = {
  'image/svg+xml': ['.svg'],
  'image/png': ['.png'],
  'image/jpeg': ['.jpg', '.jpeg'],
  'image/gif': ['.gif'],
}

const generateUniqueId = (): string => namespacedUUIDv4('bookingImage')

const formatFileSize = (sizeInBytes: number): string => {
  if (sizeInBytes < BYTES_PER_KILOBYTE) {
    return `${sizeInBytes}B`
  } else if (sizeInBytes < BYTES_PER_KILOBYTE * BYTES_PER_KILOBYTE) {
    return `${Math.round(sizeInBytes / BYTES_PER_KILOBYTE)}kb`
  } else {
    return `${Math.round(sizeInBytes / (BYTES_PER_KILOBYTE * BYTES_PER_KILOBYTE))}MB`
  }
}

const ImageUploader = ({
  uploadedImages,
  onBatchImagesAdd,
  onImageRemove,
  existingAttachments,
  onExistingAttachmentRemove,
  maxFiles = 5,
  disabled,
}: ImageUploaderProps) => {
  const [uploadingFiles, setUploadingFiles] = useState<Array<UploadingFile>>([])
  const [previewImage, setPreviewImage] = useState<string | null>(null)
  const [isProcessingQueue, setIsProcessingQueue] = useState(false)

  const uploadImageMutation = useUploadImageMutation()
  const deleteImageMutation = useDeleteImageMutation()

  // File validators
  const nameLengthValidator = (file: File) => {
    if (file?.name.length >= FILE_NAME_MAX_SIZE) {
      return {
        code: 'name-too-large',
        message: `File name is too long (max ${FILE_NAME_MAX_SIZE} characters)`,
      }
    }
    return null
  }

  // Handle rejected files
  const dropRejectedFiles = (rejectedFiles: Array<FileRejection>) => {
    for (const { file, errors } of rejectedFiles) {
      const fileName = file.name

      const invalidName = errors?.find(
        ({ code }: FileError) => code === 'name-too-large',
      )

      let errorMessage = 'File type not supported'

      if (invalidName) {
        errorMessage = 'File name is too long'
      }

      if (file.size === 0) {
        errorMessage = 'File is empty'
      }

      if (file.size > FILE_UPLOAD_MAX_SIZE) {
        errorMessage = 'File is too large (max 3MB)'
      }

      enqueueSnackbarWithCloseAction(`${errorMessage}: ${fileName}`, {
        variant: 'error',
      })
    }
  }

  const totalAttachments =
    uploadedImages.length + uploadingFiles.length + existingAttachments.length

  const processBatchUpload = useCallback(
    async (filesToUpload: Array<UploadingFile>) => {
      if (isProcessingQueue) return

      setIsProcessingQueue(true)
      const completedUploads: Array<UploadedImageData> = []

      try {
        for (const uploadingFile of filesToUpload) {
          try {
            const previewUrl = createFilePreviewUrl(uploadingFile.file)
            const base64String = await fileToBase64(uploadingFile.file)

            const response = await uploadImageMutation.mutateAsync({
              imageBase64: base64String,
            })

            const uploadedImageData: UploadedImageData = {
              guid: response.guid,
              extension: response.extension,
              fileName: uploadingFile.file.name,
              contentType: uploadingFile.file.type,
              previewUrl,
            }

            completedUploads.push(uploadedImageData)

            setUploadingFiles((current) =>
              current.filter((f) => f.id !== uploadingFile.id),
            )
          } catch (error) {
            setUploadingFiles((current) =>
              current.filter((f) => f.id !== uploadingFile.id),
            )

            enqueueSnackbarWithCloseAction(
              `Failed to upload ${uploadingFile.file.name}`,
              {
                variant: 'error',
              },
            )

            if (ENV.NODE_ENV === 'development') {
              console.error(`Failed to upload ${uploadingFile.file.name}:`, error)
            }
          }
        }

        if (completedUploads.length > 0) {
          onBatchImagesAdd(completedUploads)
        }
      } finally {
        setIsProcessingQueue(false)
      }
    },
    [uploadImageMutation, onBatchImagesAdd, isProcessingQueue],
  )

  const onDrop = useCallback(
    async (acceptedFiles: Array<FileWithPath>) => {
      if (totalAttachments + acceptedFiles.length > maxFiles) {
        enqueueSnackbarWithCloseAction(`Maximum ${maxFiles} files allowed`, {
          variant: 'warning',
        })
        return
      }

      const newUploadingFiles: Array<UploadingFile> = acceptedFiles.map((file) => ({
        file,
        id: generateUniqueId(),
        progress: 0,
      }))

      setUploadingFiles((prev) => [...prev, ...newUploadingFiles])

      await processBatchUpload(newUploadingFiles)
    },
    [totalAttachments, maxFiles, processBatchUpload],
  )

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    validator: nameLengthValidator,
    onDropRejected: dropRejectedFiles,
    accept: imageFileTypes,
    onDrop,
    maxSize: FILE_UPLOAD_MAX_SIZE,
    multiple: true,
    disabled,
  })

  const handleRemoveUploadedImage = async (guid: string) => {
    const imageToRemove = uploadedImages.find((img) => img.guid === guid)

    if (imageToRemove) {
      await deleteImageMutation.mutateAsync({
        guid: imageToRemove.guid,
        extension: imageToRemove.extension,
      })

      revokeFilePreviewUrl(imageToRemove.previewUrl)
      onImageRemove(guid)
    }
  }

  const handleImagePreview = (previewUrl: string) => {
    setPreviewImage(previewUrl)
  }

  const handleClosePreview = () => {
    setPreviewImage(null)
  }

  return (
    <Stack
      sx={{ mt: 2 }}
      gap={2}
    >
      {/* Dropzone */}
      <DropzoneWrapper
        isDragActive={isDragActive && !disabled}
        {...getRootProps()}
      >
        <DropzoneContainer>
          <input
            {...getInputProps()}
            data-testid="ImageUploader-DropzoneInput"
          />
          <DropzonePlaceholder>
            <DropzoneTextContent>
              <UploadFileIcon
                fontSize="large"
                sx={{ color: disabled ? 'action.disabled' : 'action.active' }}
              />
              <Typography color={disabled ? 'text.disabled' : 'text.primary'}>
                {isDragActive
                  ? 'Drop images here...'
                  : 'Click to upload or drag and drop'}
              </Typography>
              <Button
                size="medium"
                color="primary"
                variant="outlined"
                disabled={
                  disabled ||
                  uploadImageMutation.isPending ||
                  deleteImageMutation.isPending ||
                  isProcessingQueue
                }
              >
                Browse Files
              </Button>
              <Typography
                variant="caption"
                color="text.secondary"
              >
                {`Supported formats: ${showSupportedFileFormats([
                  'svg',
                  'png',
                  'jpg',
                  'gif',
                ])} (max. 3MB)`}
              </Typography>
            </DropzoneTextContent>
          </DropzonePlaceholder>
        </DropzoneContainer>
      </DropzoneWrapper>

      {/* Currently uploading files */}
      {uploadingFiles.length > 0 && (
        <Stack gap={1}>
          <Typography variant="subtitle2">Uploading...</Typography>
          {uploadingFiles.map((file) => (
            <UploadingFileItem
              key={file.id}
              file={file}
            />
          ))}
        </Stack>
      )}

      {/* Existing attachments */}
      {existingAttachments.length > 0 && (
        <Stack gap={1}>
          {existingAttachments.map((attachment) => (
            <ExistingAttachmentItem
              key={attachment.id}
              attachment={attachment}
              disabled={disabled}
              onRemove={() => onExistingAttachmentRemove(attachment.id)}
              onPreview={() => handleImagePreview(attachment.previewUrl)}
            />
          ))}
        </Stack>
      )}

      {/* Uploaded files list */}
      {uploadedImages.length > 0 && (
        <Stack gap={1}>
          {uploadedImages.map((imageData) => (
            <UploadedImageItem
              key={imageData.guid}
              imageData={imageData}
              disabled={disabled}
              onRemove={() => handleRemoveUploadedImage(imageData.guid)}
              onPreview={() => handleImagePreview(imageData.previewUrl)}
              isDeleting={deleteImageMutation.isPending}
            />
          ))}
        </Stack>
      )}

      {previewImage && (
        <ImageModal
          onClose={handleClosePreview}
          imageUrl={previewImage}
        />
      )}
    </Stack>
  )
}

type UploadingFileItemProps = {
  file: UploadingFile
}

const UploadingFileItem = ({ file }: UploadingFileItemProps) => (
  <UploadedFileContainer>
    <Stack
      direction="row"
      alignItems="center"
      spacing={1}
      width="100%"
    >
      <InsertDriveFileIcon sx={{ color: 'action.active' }} />

      <Stack
        flex={1}
        minWidth={0}
      >
        <Typography noWrap>{file.file.name}</Typography>
        <Typography
          variant="caption"
          color="text.secondary"
        >
          {formatFileSize(file.file.size)} • Uploading...
        </Typography>
      </Stack>

      <CircularProgress
        size={24}
        variant="indeterminate"
      />
    </Stack>
  </UploadedFileContainer>
)

type AttachmentItemProps = {
  fileName: string
  statusText?: string
  subtitle?: string
  onPreview: () => void
  onRemove: () => void
  showLoadingOnRemove?: boolean
  disabled: boolean
}

const AttachmentItem = ({
  fileName,
  statusText,
  subtitle,
  onPreview,
  onRemove,
  showLoadingOnRemove = false,
  disabled,
}: AttachmentItemProps) => (
  <UploadedFileContainer>
    <Stack
      direction="row"
      alignItems="center"
      spacing={1}
      width="100%"
    >
      <InsertDriveFileIcon sx={{ color: 'action.active' }} />

      <Stack
        flex={1}
        minWidth={0}
      >
        <Typography
          noWrap
          data-testid={`AttachmentItem-${fileName}${
            statusText ? '-existing' : ''
          }-FileName`}
        >
          {fileName}
          {statusText && (
            <Typography
              component="span"
              variant="caption"
              color="text.secondary"
              sx={{ ml: 1 }}
            >
              {statusText}
            </Typography>
          )}
        </Typography>
        {subtitle && (
          <Typography
            variant="caption"
            color="text.secondary"
          >
            {subtitle}
          </Typography>
        )}
      </Stack>

      <Stack
        direction="row"
        alignItems="center"
        spacing={0.5}
        flexShrink={0}
      >
        <Tooltip title="View">
          <IconButton
            data-testid={`AttachmentItem-${fileName}-ViewButton`}
            size="small"
            color="primary"
            onClick={onPreview}
          >
            <VisibilityIcon fontSize="small" />
          </IconButton>
        </Tooltip>

        <Tooltip title="Remove">
          <IconButton
            data-testid={`AttachmentItem-${fileName}-RemoveButton`}
            size="small"
            onClick={onRemove}
            disabled={showLoadingOnRemove || disabled}
          >
            {showLoadingOnRemove ? (
              <CircularProgress size={16} />
            ) : (
              <CloseIcon fontSize="small" />
            )}
          </IconButton>
        </Tooltip>
      </Stack>
    </Stack>
  </UploadedFileContainer>
)

type UploadedImageItemProps = {
  imageData: UploadedImageData
  onRemove: () => void
  onPreview: () => void
  isDeleting: boolean
  disabled: boolean
}

const UploadedImageItem = ({
  imageData,
  onRemove,
  onPreview,
  isDeleting,
  disabled,
}: UploadedImageItemProps) => (
  <AttachmentItem
    fileName={imageData.fileName}
    onPreview={onPreview}
    onRemove={onRemove}
    showLoadingOnRemove={isDeleting}
    disabled={disabled}
  />
)

type ExistingAttachmentItemProps = {
  attachment: ExistingAttachmentData
  onRemove: () => void
  onPreview: () => void
  disabled: boolean
}

const ExistingAttachmentItem = ({
  attachment,
  onRemove,
  onPreview,
  disabled,
}: ExistingAttachmentItemProps) => (
  <AttachmentItem
    fileName={attachment.fileName}
    statusText="(existing)"
    onPreview={onPreview}
    onRemove={onRemove}
    disabled={disabled}
  />
)

export default ImageUploader

// Styled Components
const DropzoneWrapper = MuiStyled(Box, {
  shouldForwardProp: (prop) => prop !== 'isDragActive',
})<{ isDragActive: boolean }>(({ theme, isDragActive }) =>
  theme.unstable_sx({
    display: 'grid',
    backgroundColor: isDragActive ? 'action.hover' : '#fff',
    boxShadow: '0px 0px 0px 1px #e0e0e0',
    borderRadius: '4px',
    cursor: 'pointer',
    py: 5,
    borderColor: isDragActive ? 'primary.main' : undefined,
    borderStyle: isDragActive ? 'dashed' : undefined,
    borderWidth: isDragActive ? 2 : undefined,
  }),
)

const DropzoneContainer = MuiStyled(Box)({
  height: '100%',
  outline: 'none',
})

const DropzonePlaceholder = MuiStyled(Stack)({
  flexDirection: 'row',
  alignItems: 'center',
  color: '#666',
  flex: '1',
  justifyContent: 'center',
  height: '100%',
})

const DropzoneTextContent = MuiStyled(Stack)(({ theme }) =>
  theme.unstable_sx({
    alignItems: 'center',
    gap: 2,
  }),
)

const UploadedFileContainer = MuiStyled(Box)(({ theme }) =>
  theme.unstable_sx({
    padding: 1.5,
    borderRadius: 1,
    border: '1px solid',
    borderColor: 'divider',
    position: 'relative',
    overflow: 'hidden',
  }),
)

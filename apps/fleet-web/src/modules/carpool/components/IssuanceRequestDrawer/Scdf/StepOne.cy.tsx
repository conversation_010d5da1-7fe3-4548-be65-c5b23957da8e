/// <reference types="@testing-library/cypress" />
import { zodResolver } from '@hookform/resolvers/zod'
import { <PERSON><PERSON>, Stack } from '@karoo-ui/core'
import { useForm } from 'react-hook-form'

import { duxsMocks } from 'src/cypress-ct/mocks/duxs'
import { getInputInsideByTestId, mountWithProviders } from 'src/cypress-ct/utils'
import { messages } from 'src/shared/formik'

import { generateBookingSchema, type BookingFormSchema } from './schema'
import StepOne from './StepOne'
import type { BookingPurposeAutocompleteOption } from './types'

// Mock data for testing
const mockBookingPurposeOptions = {
  array: [
    { id: 1, label: 'Official Business' },
    { id: 2, label: 'Training' },
    { id: 3, label: 'Others' },
  ] as Array<BookingPurposeAutocompleteOption>,
  byId: new Map([
    [1, { id: 1, label: 'Official Business' }],
    [2, { id: 2, label: 'Training' }],
    [3, { id: 3, label: 'Others' }],
  ]),
}

const defaultFormValues: BookingFormSchema = {
  purposeOfRequest: null,
  requestDescription: '',
  requestor: 'test.user',
  bookingForOtherParty: false,
  requestedForClientUserId: null,
  vehicleTypeId: null,
  vehicleId: null,
  numberOfPassengers: null,
  driverSelection: 'any' as const,
  driverId: null,
  vehicleCommanderSelection: 'any' as const,
  vehicleCommanderId: null,
  pickupTime: null,
  pickupLocation: null,
  dropoffTime: null,
  journeyType: 'return' as const,
  journeys: [],
  equipmentType: 'no-equipment' as const,
  equipmentIds: [],
  uploadedImages: [],
  equipmentAttachmentIds: [],
  remarks: '',
}

// Test wrapper component
function TestStepOne({
  disabled = false,
  initialValues = defaultFormValues,
}: {
  disabled?: boolean
  initialValues?: Partial<BookingFormSchema>
}) {
  const schema = generateBookingSchema({
    carpoolBookingInAdvance: 24,
    carpoolBookingInAdvanceUnit: 'hours',
    carpoolMaximumBookingTime: 8,
    carpoolMaximumBookingTimeUnit: 'hours',
    carpoolAllowBackDateBooking: false,
    bookingPurposeByIdMap: mockBookingPurposeOptions.byId,
    mode: 'create',
  })

  const { control, trigger } = useForm<BookingFormSchema>({
    resolver: zodResolver(schema),
    mode: 'onChange',
    defaultValues: { ...defaultFormValues, ...initialValues },
  })

  const handleNext = async () =>
    await trigger([
      'purposeOfRequest',
      'requestDescription',
      'requestor',
      'bookingForOtherParty',
      'requestedForClientUserId',
    ])

  return (
    <Stack>
      <StepOne
        control={control}
        bookingPurposeOptions={mockBookingPurposeOptions}
        disabled={disabled}
      />
      <Button
        onClick={handleNext}
        data-testid="StepOne-NextButton"
      >
        Next
      </Button>
    </Stack>
  )
}

const mountStepOne = (props?: Parameters<typeof TestStepOne>[0]) => {
  mountWithProviders(<TestStepOne {...props} />, {
    reduxOptions: {
      preloadedState: {
        user: duxsMocks.user().mockState,
      },
    },
  })
}

// Test selectors using data-testid
const purposeDataTestId = 'StepOne-PurposeOfRequest'
const descriptionDataTestId = 'StepOne-RequestDescription'
const requestorDataTestId = 'StepOne-Requestor'
const bookingForOtherPartyDataTestId = 'StepOne-BookingForOtherParty'
const requestedForDataTestId = 'StepOne-RequestedFor'

describe('StepOne Component', () => {
  describe('Basic Rendering', () => {
    it('should render all form fields correctly', () => {
      mountStepOne()

      // Check section title
      cy.findByTestId('StepOne-Title').should('be.visible')

      // Check purpose of request field
      cy.findByTestId(purposeDataTestId).should('be.visible')
      getInputInsideByTestId(purposeDataTestId).should('have.attr', 'required')

      // Check request description field
      cy.findByTestId(descriptionDataTestId).should('be.visible')

      // Check requestor field (should be disabled)
      cy.findByTestId(requestorDataTestId).should('be.visible')
      getInputInsideByTestId(requestorDataTestId).should('be.disabled')

      // Check booking for other party checkbox
      cy.findByTestId(bookingForOtherPartyDataTestId).should('be.visible')
      getInputInsideByTestId(bookingForOtherPartyDataTestId).should('not.be.checked')
    })

    it('should display correct initial values', () => {
      mountStepOne({
        initialValues: {
          requestor: 'john.doe',
          requestDescription: 'Initial description',
        },
      })

      getInputInsideByTestId(requestorDataTestId).should('have.value', 'john.doe')
      getInputInsideByTestId(descriptionDataTestId).should(
        'have.value',
        'Initial description',
      )
    })
  })

  describe('Fill Form', () => {
    it('should fill form and validate it correctly', () => {
      mountStepOne()

      // Initially unchecked
      cy.findByTestId(bookingForOtherPartyDataTestId).should('not.be.checked')
      // Initially, user selection should not be visible
      cy.findByTestId(requestedForDataTestId).should('not.exist')

      // trigger the form
      cy.findByTestId('StepOne-NextButton').click()

      // Check for validation error
      cy.findByTestId(purposeDataTestId)
        .find('.MuiFormHelperText-root')
        .should('contain.text', messages.required)

      // Open dropdown
      cy.findByTestId(purposeDataTestId).click()

      // Check all options are visible
      cy.contains(mockBookingPurposeOptions.array[0].label).should('be.visible')
      cy.contains(mockBookingPurposeOptions.array[1].label).should('be.visible')
      cy.contains(mockBookingPurposeOptions.array[2].label).should('be.visible')

      // Select an option
      cy.contains(mockBookingPurposeOptions.array[1].label).click()

      // Verify selection
      getInputInsideByTestId(purposeDataTestId).should(
        'have.value',
        mockBookingPurposeOptions.array[1].label,
      )

      const testDescription = 'This is a test description for the booking request'
      getInputInsideByTestId(descriptionDataTestId).type(testDescription)
      getInputInsideByTestId(descriptionDataTestId).should(
        'have.value',
        testDescription,
      )

      // clear description field
      getInputInsideByTestId(descriptionDataTestId).type('{selectall}{backspace}')

      // Select "Others" purpose
      cy.findByTestId(purposeDataTestId).click()
      cy.contains(mockBookingPurposeOptions.array[2].label).click()

      // Description field should now be required
      getInputInsideByTestId(descriptionDataTestId).should('have.attr', 'required')

      // trigger the form
      cy.findByTestId('StepOne-NextButton').click()

      // Should show validation error
      cy.findByTestId(descriptionDataTestId)
        .find('.MuiFormHelperText-root')
        .should('contain.text', messages.required)

      // Select non-"Others" purpose
      cy.findByTestId(purposeDataTestId).click()
      cy.contains(mockBookingPurposeOptions.array[1].label).click()

      // Description field should not be required
      getInputInsideByTestId(descriptionDataTestId).should('not.have.attr', 'required')

      // Click to check
      cy.findByTestId(bookingForOtherPartyDataTestId).click()
      getInputInsideByTestId(bookingForOtherPartyDataTestId).should('be.checked') // User selection field should now be visible
      cy.findByTestId(requestedForDataTestId).should('be.visible')
      getInputInsideByTestId(requestedForDataTestId).should('have.attr', 'required')

      // trigger the form
      cy.findByTestId('StepOne-NextButton').click()

      // Should show validation error for requested
      cy.findByTestId(requestedForDataTestId)
        .find('.MuiFormHelperText-root')
        .should('contain.text', messages.required)

      // Click to uncheck
      cy.findByTestId(bookingForOtherPartyDataTestId).click()
      getInputInsideByTestId(bookingForOtherPartyDataTestId).should('not.be.checked')

      // trigger the form
      cy.findByTestId('StepOne-NextButton').click()

      cy.findByTestId(descriptionDataTestId)
        .find('.MuiFormHelperText-root')
        .should('not.exist')

      cy.findByTestId(purposeDataTestId)
        .find('.MuiFormHelperText-root')
        .should('not.exist')
    })

    it('should hide user selection field when checkbox is unchecked', () => {
      mountStepOne({
        initialValues: {
          bookingForOtherParty: true,
        },
      })

      // User selection should be visible initially
      cy.findByTestId(requestedForDataTestId).should('be.visible')

      // Uncheck the checkbox
      cy.findByTestId(bookingForOtherPartyDataTestId).click()

      // User selection field should be hidden
      cy.findByTestId(requestedForDataTestId).should('not.exist')
    })
  })

  describe('Disabled State', () => {
    it('should disable user selection field when disabled prop is true', () => {
      mountStepOne({
        disabled: true,
        initialValues: {
          bookingForOtherParty: true,
        },
      })

      // All interactive fields should be disabled
      getInputInsideByTestId(purposeDataTestId).should('be.disabled')
      getInputInsideByTestId(descriptionDataTestId).should('be.disabled')
      getInputInsideByTestId(bookingForOtherPartyDataTestId).should('be.disabled')

      getInputInsideByTestId(requestedForDataTestId).should('be.disabled')
    })
  })
})

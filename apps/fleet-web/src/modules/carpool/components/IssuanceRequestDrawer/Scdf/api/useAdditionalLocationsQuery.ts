import { useQuery } from '@tanstack/react-query'

import { makeQueryErrorHandlerWithToast } from 'api/helpers'
import type { BookingAdditionalLocationId } from 'api/types'
import { restGet } from 'src/api/rest-api-caller'

export declare namespace FetchAdditionalLocations {
  type AdditionalLocation = {
    id: number
    locationName: string
    description: string
  }

  type Response = { data: Array<AdditionalLocation> }
}

function getAdditionalLocations() {
  return restGet<FetchAdditionalLocations.Response>(
    '/booking/additionallocations',
  ).then((res) =>
    res.data.map((location) => ({
      ...location,
      id: location.id as BookingAdditionalLocationId,
    })),
  )
}

const createKey = () => ['booking/additionalLocations']

const useAdditionalLocationsQuery = () =>
  useQuery({
    queryKey: createKey(),
    queryFn: getAdditionalLocations,
    ...makeQueryErrorHandlerWithToast(),
  })

export default Object.assign(useAdditionalLocationsQuery, {
  createKey,
})

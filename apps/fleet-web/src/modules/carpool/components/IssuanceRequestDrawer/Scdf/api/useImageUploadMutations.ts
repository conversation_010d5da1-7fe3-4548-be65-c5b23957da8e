import { useMutation } from '@tanstack/react-query'

import { restPost } from 'src/api/rest-api-caller'
import { enqueueSnackbarWithCloseAction } from 'src/components/Snackbar/Notistack/utils'

export declare namespace UploadImageApi {
  type Request = {
    imageBase64: string
  }

  type Response = {
    guid: string
    contentType: string
    extension: string
    status: string
  }
}

export declare namespace DeleteImageApi {
  type Request = {
    guid: string
    extension: string
  }

  type Response = {
    guid: string
    extension: string
    status: string
  }
}

function uploadImageTemp(
  params: UploadImageApi.Request,
): Promise<UploadImageApi.Response> {
  return restPost<UploadImageApi.Response>(
    '/scdf/booking/uploadSingleImageTemp',
    params,
  )
}

function deleteTempImage(
  params: DeleteImageApi.Request,
): Promise<DeleteImageApi.Response> {
  return restPost<DeleteImageApi.Response>(
    '/scdf/booking/deleteTempSingleImage',
    params,
  )
}

export const useUploadImageMutation = () =>
  useMutation({
    mutationFn: uploadImageTemp,
    onSuccess() {
      enqueueSnackbarWithCloseAction('Image uploaded successfully', {
        variant: 'success',
      })
    },
    onError() {
      enqueueSnackbarWithCloseAction('Failed to upload image.', {
        variant: 'error',
      })
    },
  })

export const useDeleteImageMutation = () =>
  useMutation({
    mutationFn: deleteTempImage,
    onSuccess() {
      enqueueSnackbarWithCloseAction('File removed', { variant: 'success' })
    },
    onError() {
      enqueueSnackbarWithCloseAction('Failed to delete image.', { variant: 'error' })
    },
  })

import { useMemo } from 'react'
import * as R from 'remeda'

import type { BookingEquipmentId } from 'api/types'

import useCarpoolOptionsQuery, {
  type FetchCarpoolOptionsParsedData,
} from '../../../../queries/useCarpoolOptionsQuery'
import useEquipmentsQuery from '../api/useEquipmentsQuery'
import type {
  BookingPurposeAutocompleteOption,
  EquipmentAutocompleteOption,
  LocationAutocompleteOption,
} from '../types'

export function useBookingOptions() {
  const carpoolOptionsQuery = useCarpoolOptionsQuery()
  const equipmentsQuery = useEquipmentsQuery()

  const locationOptions = useMemo(() => {
    const array: Array<LocationAutocompleteOption> = []
    const byId = new Map<number, LocationAutocompleteOption>()

    if (carpoolOptionsQuery.data && !R.isEmpty(carpoolOptionsQuery.data)) {
      for (const location of carpoolOptionsQuery.data.locations) {
        const option = { id: location.id, label: location.name }

        array.push(option)
        byId.set(option.id, option)
      }
    }
    return {
      array,
      byId,
    }
  }, [carpoolOptionsQuery.data])

  const equipmentOptions = useMemo(() => {
    const array: Array<EquipmentAutocompleteOption> = []
    const byId = new Map<BookingEquipmentId, EquipmentAutocompleteOption>()

    if (equipmentsQuery.data && !R.isEmpty(equipmentsQuery.data.data)) {
      for (const equipment of equipmentsQuery.data.data) {
        const option = { id: equipment.id, label: equipment.name }

        array.push(option)
        byId.set(option.id, option)
      }
    }
    return { array, byId }
  }, [equipmentsQuery.data])

  const bookingPurposeOptions = useMemo(() => {
    const array: Array<BookingPurposeAutocompleteOption> = []
    const byId = new Map<number, BookingPurposeAutocompleteOption>()

    if (carpoolOptionsQuery.data && !R.isEmpty(carpoolOptionsQuery.data)) {
      for (const purpose of carpoolOptionsQuery.data.bookingPurposes) {
        const option = { id: purpose.id, label: purpose.purpose }

        array.push(option)
        byId.set(option.id, option)
      }
    }
    return {
      array,
      byId,
    }
  }, [carpoolOptionsQuery.data])

  return {
    locationOptions,
    equipmentOptions,
    bookingPurposeOptions,
    // Also return the raw queries in case they're needed
    carpoolOptionsData:
      carpoolOptionsQuery.data ?? ({} as FetchCarpoolOptionsParsedData),
    equipmentsQuery,
    isPending: carpoolOptionsQuery.isPending || equipmentsQuery.isPending,
  }
}

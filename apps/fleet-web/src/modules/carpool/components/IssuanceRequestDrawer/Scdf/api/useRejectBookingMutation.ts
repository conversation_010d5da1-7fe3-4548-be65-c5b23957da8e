import { useMutation } from '@tanstack/react-query'

import { restPatch } from 'src/api/rest-api-caller'
import { enqueueSnackbarWithCloseAction } from 'src/components/Snackbar/Notistack/utils'

import { ctIntl } from 'cartrack-ui-kit'
import type { ScdfRejectBooking } from './types'
import useScdfBookingMutationInvalidation from './useScdfBookingMutationInvalidation'

function rejectBooking(
  params: ScdfRejectBooking.Params,
): Promise<ScdfRejectBooking.Response> {
  const requestPayload: ScdfRejectBooking.RequestPayload = {
    bookingId: Number(params.bookingId),
    rejectClientUserId: params.userId,
    bookingRejectReasonId: params.reasonId,
    bookingRejectReason: params.remarks || '',
  }

  return restPatch<ScdfRejectBooking.Response>(`/scdf/booking/reject`, requestPayload)
}

const useRejectBookingMutation = () => {
  const invalidateBookingQueries = useScdfBookingMutationInvalidation()

  return useMutation({
    mutationFn: rejectBooking,
    onSuccess() {
      enqueueSnackbarWithCloseAction(
        ctIntl.formatMessage({ id: 'Reject booking successfully.' }),
        { variant: 'success' },
      )
    },
    onSettled: (_, __, variables) => {
      invalidateBookingQueries({
        shouldInvalidateSpecificBooking: true,
        bookingId: variables.bookingId,
      })
    },
    onError: (error) => {
      enqueueSnackbarWithCloseAction(error.message, { variant: 'error' })
    },
  })
}

export default useRejectBookingMutation

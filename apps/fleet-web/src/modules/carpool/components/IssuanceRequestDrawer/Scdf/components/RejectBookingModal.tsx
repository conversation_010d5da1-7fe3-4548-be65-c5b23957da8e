import { useMemo } from 'react'
import { zodResolver } from '@hookform/resolvers/zod'
import { Autocomplete, Stack, TextField } from '@karoo-ui/core'
import { Controller, useForm } from 'react-hook-form'
import { z } from 'zod'

import type { CarpoolBookingId } from 'api/types'
import { getAutocompleteVirtualizedProps } from 'src/components/_dropdowns/AutocompleteVirtualized'
import ConfirmationModal from 'src/components/_modals/Confirmation'
import { messages } from 'src/shared/formik'
import IntlTypography from 'src/util-components/IntlTypography'

import { ctIntl } from 'cartrack-ui-kit'
import useRejectReasonsQuery from '../api/useRejectReasonsQuery'
import type { RejectBookingFormData, RejectReasonOption } from '../types'

const rejectBookingSchema = z.object({
  reasonId: z.number().min(1, { message: messages.required }),
  remarks: z.string().default(''),
})

type RejectBookingModalProps = {
  onClose: () => void
  onConfirm: (data: RejectBookingFormData) => void
  isLoading: boolean
  bookingId: CarpoolBookingId
}

const RejectBookingModal = ({
  bookingId,
  onClose,
  onConfirm,
  isLoading,
}: RejectBookingModalProps) => {
  const rejectReasonsQuery = useRejectReasonsQuery()

  const {
    control,
    handleSubmit,
    reset,
    formState: { isValid },
  } = useForm<RejectBookingFormData>({
    resolver: zodResolver(rejectBookingSchema),
    mode: 'onChange',
    defaultValues: { reasonId: null, remarks: '' },
  })

  const rejectReasonOptions = useMemo(() => {
    const array: Array<RejectReasonOption> = []
    const byId = new Map<number, RejectReasonOption>()

    if (rejectReasonsQuery.data) {
      for (const reason of rejectReasonsQuery.data) {
        const option = { id: reason.id, label: reason.title }
        array.push(option)
        byId.set(option.id, option)
      }
    }

    return { array, byId }
  }, [rejectReasonsQuery.data])

  const handleModalClose = () => {
    reset()
    onClose()
  }

  const handleFormSubmit = handleSubmit((data) => {
    onConfirm(data)
  })

  return (
    <ConfirmationModal
      open={true}
      onClose={handleModalClose}
      onConfirm={handleFormSubmit}
      title="Reject Booking Request"
      confirmButtonLabel="CONFIRM"
      confirmButtonVariant="contained"
      isLoading={isLoading}
      disabledConfirmButton={!isValid}
      data-testid="RejectBookingModal"
    >
      <Stack gap={2}>
        <IntlTypography
          color="text.secondary"
          msgProps={{
            id: 'tfms.reject.selectReason.description',
            values: { bookingId },
          }}
        />
        <Controller
          control={control}
          name="reasonId"
          render={({ field, fieldState }) => (
            <Autocomplete<RejectReasonOption>
              size="small"
              loading={rejectReasonsQuery.status === 'pending'}
              data-testid="RejectBookingModal-RejectReason"
              {...getAutocompleteVirtualizedProps({
                options: rejectReasonOptions.array,
              })}
              onChange={(_, newValue) => {
                field.onChange(newValue ? newValue.id : null)
              }}
              value={
                field.value ? rejectReasonOptions.byId.get(field.value) ?? null : null
              }
              renderInput={(params) => (
                <TextField
                  {...params}
                  required
                  label="Select reason why booking is declined"
                  helperText={ctIntl.formatMessage({
                    id: fieldState.error?.message ?? '',
                  })}
                  error={!!fieldState.error}
                />
              )}
            />
          )}
        />

        <Controller
          control={control}
          name="remarks"
          render={({ field }) => (
            <TextField
              {...field}
              label="Remarks"
              multiline
              rows={3}
              variant="outlined"
              size="small"
              fullWidth
              data-testid="RejectBookingModal-Remarks"
            />
          )}
        />
      </Stack>
    </ConfirmationModal>
  )
}

export default RejectBookingModal

/// <reference types="@testing-library/cypress" />
import { DateTime } from 'luxon'

import { duxsMocks } from 'src/cypress-ct/mocks/duxs'
import { getInputInsideByTestId, mountWithProviders } from 'src/cypress-ct/utils'
import { messages } from 'src/shared/formik'

import { BookingStatus } from '../../../utils/constants'
import CreateEditBookingDrawer from './CreateEditBookingDrawer'
import type { BookingFormSchema } from './schema'
import { STEPS } from './types'

// Mock data for testing
const mockInitialValues: BookingFormSchema = {
  purposeOfRequest: 1,
  requestDescription: 'Test description',
  requestor: 'test.user',
  bookingForOtherParty: false,
  requestedForClientUserId: null,
  vehicleTypeId: 1,
  vehicleId: null,
  numberOfPassengers: 2,
  driverSelection: 'any' as const,
  driverId: null,
  vehicleCommanderSelection: 'any' as const,
  vehicleCommanderId: null,
  pickupTime: DateTime.now().plus({ hours: 2 }).toJSDate(),
  pickupLocation: 1,
  dropoffTime: DateTime.now().plus({ hours: 4 }).toJSDate(),
  journeyType: 'return' as const,
  journeys: [],
  equipmentType: 'no-equipment' as const,
  equipmentIds: [],
  uploadedImages: [],
  equipmentAttachmentIds: [],
  remarks: 'Test remarks',
}

// Test wrapper component
function TestCreateEditBookingDrawer({
  mode = 'create',
  onClose = cy.stub(),
  onSubmit = cy.stub(),
  onSubmitLoading = false,
  initialValues,
  bookingId,
  bookingStatusId,
  isKeyCollected = false,
}: {
  mode?: 'create' | 'edit' | 'duplicate'
  onClose?: () => void
  onSubmit?: (values: any) => void
  onSubmitLoading?: boolean
  initialValues?: BookingFormSchema
  bookingId?: string
  bookingStatusId?: string
  isKeyCollected?: boolean
}) {
  return (
    <CreateEditBookingDrawer
      onClose={onClose}
      onSubmit={onSubmit}
      onSubmitLoading={onSubmitLoading}
      {...(mode === 'create'
        ? {}
        : {
            initialValues: initialValues || mockInitialValues,
            attachmentsQuery: {
              data: [],
              status: 'success',
              isPending: false,
              isError: false,
            },
            ...(mode === 'edit'
              ? {
                  bookingId: bookingId || '123',
                  bookingStatusId:
                    bookingStatusId || BookingStatus.BOOKING_STATUS_REQUESTED,
                  isKeyCollected,
                }
              : {}),
          })}
    />
  )
}

const mountCreateEditBookingDrawer = (
  props?: Parameters<typeof TestCreateEditBookingDrawer>[0],
) => {
  // Mock API calls
  cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
    if (req.body.method === 'ct_fleet_get_booking_options') {
      req.reply({
        body: {
          id: 10,
          result: {
            bookingPurposes: [
              { id: 1, name: 'Official Business' },
              { id: 2, name: 'Training' },
            ],
            locations: [
              { id: 1, name: 'Main Office' },
              { id: 2, name: 'Branch Office' },
            ],
            vehicleTypes: [
              { id: 1, name: 'Sedan' },
              { id: 2, name: 'SUV' },
            ],
            equipments: [
              { id: 1, name: 'Laptop' },
              { id: 2, name: 'Projector' },
            ],
          },
        },
      })
    }
    if (req.body.method === 'ct_fleet_get_users') {
      req.reply({
        body: {
          id: 10,
          result: [
            { id: 1, username: 'john.doe', email: '<EMAIL>' },
            { id: 2, username: 'jane.smith', email: '<EMAIL>' },
          ],
        },
      })
    }
    if (req.body.method === 'ct_fleet_get_carpool_booking_time_rules') {
      req.reply({
        body: {
          id: 10,
          result: {
            carpoolBookingInAdvance: 24,
            carpoolBookingInAdvanceUnit: 'hours',
            carpoolMaximumBookingTime: 8,
            carpoolMaximumBookingTimeUnit: 'hours',
          },
        },
      })
    }
  }).as('getBookingOptions')

  mountWithProviders(<TestCreateEditBookingDrawer {...props} />, {
    reduxOptions: {
      preloadedState: {
        user: duxsMocks.user().mockState,
      },
    },
  })
}

describe('CreateEditBookingDrawer Component', () => {
  describe('Basic Rendering', () => {
    it('should render stepper and form correctly in create mode', () => {
      mountCreateEditBookingDrawer({ mode: 'create' })

      // Check header title
      cy.findByTestId('BookingDrawerLayout-Title').should('have.text', 'Create Booking')

      // Check stepper
      cy.get('.MuiStepper-root').should('be.visible')
      cy.get('.MuiStep-root').should('have.length', 3)

      // Check step labels
      cy.findByTestId('CreateEditBookingDrawer-StepLabel-' + STEPS[0].id).should(
        'contain.text',
        STEPS[0].label,
      )
      cy.findByTestId('CreateEditBookingDrawer-StepLabel-' + STEPS[1].id).should(
        'contain.text',
        STEPS[1].label,
      )
      cy.findByTestId('CreateEditBookingDrawer-StepLabel-' + STEPS[2].id).should(
        'contain.text',
        STEPS[2].label,
      )

      // Check action buttons
      cy.findByTestId('CreateEditBookingDrawer-Cancel').should('be.visible')
      cy.findByTestId('CreateEditBookingDrawer-Next').should('be.visible')

      // First step should be active
      cy.findByTestId('CreateEditBookingDrawer-Step-0').should(
        'have.class',
        'Mui-active',
      )
    })

    it('should render correctly in edit mode', () => {
      mountCreateEditBookingDrawer({
        mode: 'edit',
        bookingId: '123',
        bookingStatusId: BookingStatus.BOOKING_STATUS_REQUESTED,
      })

      // Check header title
      cy.findByTestId('BookingDrawerLayout-Title').should('have.text', 'Edit Booking')

      // Should show stepper
      cy.get('.MuiStepper-root').should('be.visible')
    })

    it('should render correctly in duplicate mode', () => {
      mountCreateEditBookingDrawer({ mode: 'duplicate' })

      // Check header title
      cy.findByTestId('BookingDrawerLayout-Title').should('have.text', 'Create Booking')
    })
  })

  describe('Stepper Navigation', () => {
    it('should navigate through steps correctly', () => {
      mountCreateEditBookingDrawer({ mode: 'create' })

      // Fill step 1 and go to next
      cy.findByTestId('StepOne-PurposeOfRequest').click()
      cy.contains('Official Business').click()
      cy.findByTestId('StepOne-RequestDescription').type('Test description')

      cy.findByTestId('CreateEditBookingDrawer-Next').click()

      // Should be at step 2
      cy.findByTestId('CreateEditBookingDrawer-Step-1').should(
        'have.class',
        'Mui-active',
      )

      // Check back button appears
      cy.findByTestId('CreateEditBookingDrawer-Back').should('be.visible')
      cy.findByTestId('CreateEditBookingDrawer-Next').should('be.visible')

      // Go back to step 1
      cy.get('[data-testid="CreateEditBookingDrawer-Back"]').click()
      cy.findByTestId('CreateEditBookingDrawer-Step-0').should(
        'have.class',
        'Mui-active',
      )
      cy.findByTestId('CreateEditBookingDrawer-Step-1').should(
        'not.have.class',
        'Mui-active',
      )
    })
  })

  describe('Form Submission', () => {
    it('should call onSubmit when form is valid and submitted', () => {
      const onSubmit = cy.stub().as('onSubmit')
      mountCreateEditBookingDrawer({ mode: 'create', onSubmit })

      // Step 1: Fill required fields
      cy.findByTestId('StepOne-PurposeOfRequest').click()
      cy.contains('Official Business').click()
      cy.findByTestId('StepOne-RequestDescription').type('Test booking description')

      // Navigate to step 2
      cy.findByTestId('CreateEditBookingDrawer-Next').click()

      // Step 2: Fill required fields
      cy.findByTestId('StepTwo-PickupTime').click()
      cy.get('.MuiPickersDay-root[aria-label*="tomorrow"]').first().click()
      cy.get('.MuiTimeClock-root').should('be.visible')
      cy.get('[data-testid="accept-time"]').click()

      cy.findByTestId('StepTwo-DropoffTime').click()
      cy.get('.MuiPickersDay-root[aria-label*="tomorrow"]').first().click()
      cy.get('.MuiTimeClock-root').should('be.visible')
      cy.get('[data-testid="accept-time"]').click()

      cy.findByTestId('StepTwo-PickupLocation').click()
      cy.contains('Main Office').click()

      // Navigate to step 3
      cy.findByTestId('CreateEditBookingDrawer-Next').click()

      // Step 3: Fill required fields
      cy.findByTestId('StepThree-VehicleType').click()
      cy.contains('Sedan').click()

      cy.findByTestId('StepThree-NumberOfPassengers').type('3')

      // Submit the form
      cy.findByTestId('CreateEditBookingDrawer-Save').click()

      // Verify onSubmit was called
      cy.get('@onSubmit').should('have.been.called')
    })

    it('should disable Save button when form is invalid', () => {
      mountCreateEditBookingDrawer({ mode: 'create' })

      // Navigate through steps to reach Save button
      // The Save button should be disabled if form is invalid
      cy.get('.MuiStepper-root').should('be.visible')
    })

    it('should show loading state during submission', () => {
      mountCreateEditBookingDrawer({
        mode: 'create',
        onSubmitLoading: true,
      })

      // Loading state would be shown on the Save button
      cy.get('.MuiStepper-root').should('be.visible')
    })
  })

  describe('Edit Mode Specific Behavior', () => {
    it('should disable form when booking status is not editable', () => {
      mountCreateEditBookingDrawer({
        mode: 'edit',
        bookingStatusId: BookingStatus.BOOKING_STATUS_CANCELLED,
      })

      // Form fields should be disabled
      cy.get('[data-testid="StepOne-PurposeOfRequest"]').should('be.disabled')
    })

    it('should allow editing when booking status is editable', () => {
      mountCreateEditBookingDrawer({
        mode: 'edit',
        bookingStatusId: BookingStatus.BOOKING_STATUS_REQUESTED,
      })

      // Form fields should be enabled
      cy.get('[data-testid="StepOne-PurposeOfRequest"]').should('not.be.disabled')
    })

    it('should disable vehicle selection when key is collected', () => {
      mountCreateEditBookingDrawer({
        mode: 'edit',
        isKeyCollected: true,
      })

      // Vehicle-related fields should be disabled
      cy.get('.MuiStepper-root').should('be.visible')
    })
  })

  describe('Duplicate Mode Specific Behavior', () => {
    it('should clear time-sensitive fields in duplicate mode', () => {
      mountCreateEditBookingDrawer({
        mode: 'duplicate',
        initialValues: mockInitialValues,
      })

      // Time fields should be cleared
      // Vehicle and driver selections should be reset
      cy.get('.MuiStepper-root').should('be.visible')
    })
  })

  describe('Close Functionality', () => {
    it('should call onClose when Cancel button is clicked', () => {
      const onClose = cy.stub().as('onClose')
      mountCreateEditBookingDrawer({ mode: 'create', onClose })

      cy.contains('Cancel').click()
      cy.get('@onClose').should('have.been.called')
    })

    it('should call onClose when Back button is clicked on first step', () => {
      const onClose = cy.stub().as('onClose')
      mountCreateEditBookingDrawer({ mode: 'create', onClose })

      // On first step, only Cancel button should close
      cy.get('[data-testid="CreateEditBookingDrawer-Cancel"]').click()
      cy.get('@onClose').should('have.been.called')
    })
  })

  describe('Auto-selection Behavior', () => {
    it('should auto-select single options in create mode', () => {
      // Mock single option responses
      cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
        if (req.body.method === 'ct_fleet_get_booking_options') {
          req.reply({
            body: {
              id: 10,
              result: {
                bookingPurposes: [{ id: 1, name: 'Official Business' }],
                locations: [{ id: 1, name: 'Main Office' }],
                vehicleTypes: [{ id: 1, name: 'Sedan' }],
              },
            },
          })
        }
      })

      mountCreateEditBookingDrawer({ mode: 'create' })

      // Single options should be auto-selected
      cy.get('[data-testid="StepOne-PurposeOfRequest"]').should(
        'contain.text',
        'Official Business',
      )
    })
  })
})

import { Box, IconButton, Modal, styled as MuiStyled } from '@karoo-ui/core'
import CloseIcon from '@mui/icons-material/Close'

export default function ImageModal({
  imageUrl,
  onClose,
}: {
  imageUrl: string
  onClose: () => void
}) {
  return (
    <Modal
      open
      onClose={onClose}
      aria-labelledby="image-preview-modal"
      data-testid="ImagePreviewModal"
    >
      <ImagePreviewContainer>
        <IconButton
          data-testid="ImagePreviewModal-CloseButton"
          size="small"
          onClick={onClose}
          sx={{
            position: 'absolute',
            top: 8,
            right: 8,
            zIndex: 1,
            bgcolor: 'background.paper',
            '&:hover': {
              bgcolor: 'action.hover',
            },
          }}
        >
          <CloseIcon />
        </IconButton>
        <ImageWrapper>
          <PreviewImage
            src={imageUrl}
            alt="Preview"
          />
        </ImageWrapper>
      </ImagePreviewContainer>
    </Modal>
  )
}

const ImagePreviewContainer = MuiStyled(Box)(({ theme }) =>
  theme.unstable_sx({
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    backgroundColor: 'background.paper',
    boxShadow: 24,
    borderRadius: 1,
    padding: 2,
    width: 'auto',
    height: 'auto',
    maxWidth: 'calc(90vw - 32px)',
    maxHeight: 'calc(90vh - 32px)',
    outline: 'none',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  }),
)

const ImageWrapper = MuiStyled(Box)(({ theme }) =>
  theme.unstable_sx({
    position: 'relative',
    width: '100%',
    height: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    overflow: 'hidden',
  }),
)

const PreviewImage = MuiStyled('img')(({ theme }) =>
  theme.unstable_sx({
    display: 'block',
    maxWidth: '100%',
    maxHeight: 'calc(90vh - 64px)',
    width: 'auto',
    height: 'auto',
    objectFit: 'contain',
  }),
)

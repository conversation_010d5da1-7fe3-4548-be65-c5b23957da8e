import { useEffect, useMemo } from 'react'
import type { FetchStatus } from '@tanstack/react-query'
import { useDispatch } from 'react-redux'

import {
  getActiveDrivers,
  getDriverGroupsMapById,
  getDrivers,
  getDriversById,
  getDriversOptionsMeta,
  getDriversQuery,
  triggerDriversQueryIfStatusPendingOrError,
} from 'duxs/drivers'
import { useEffectEvent } from 'src/hooks/useEventHandler'
import { useTypedSelector } from 'src/redux-hooks'

type DataObject = {
  activeAndInactiveDrivers: ReturnType<typeof getDrivers>
  activeDrivers: ReturnType<typeof getActiveDrivers>
  driversOptionsMeta: ReturnType<typeof getDriversOptionsMeta>
  driverGroupsMapById: ReturnType<typeof getDriverGroupsMapById>
  allDriversById: ReturnType<typeof getDriversById>
}
export type UseDriversQueryData = DataObject

export type UseDriversQueryReturnWithData<SelectedData = UseDriversQueryData> =
  | {
      status: 'success'
      fetchStatus: FetchStatus
      data: SelectedData
    }
  | {
      status: 'error'
      fetchStatus: FetchStatus
      data: SelectedData
    }
type UseDriversQueryReturn<SelectedData> =
  | {
      status: 'pending'
      fetchStatus: FetchStatus
      data: undefined
    }
  | UseDriversQueryReturnWithData<SelectedData>

/**
 * react-query like hook for our redux drivers state.
 * Makes things a bit easier for us, for now.
 */
export function useDriversQuery<SelectedData = UseDriversQueryData>({
  select,
}: {
  select?: (data: UseDriversQueryData) => SelectedData
} = {}): UseDriversQueryReturn<SelectedData> {
  const dispatch = useDispatch()
  const driversQuery = useTypedSelector(getDriversQuery)
  const activeAndInactiveDrivers = useTypedSelector(getDrivers)
  const activeDrivers = useTypedSelector(getActiveDrivers)
  const driverGroupsMapById = useTypedSelector(getDriverGroupsMapById)
  const allDriversById = useTypedSelector(getDriversById)
  const driversOptionsMeta = useTypedSelector(getDriversOptionsMeta)

  const onMount = useEffectEvent(() => {
    dispatch(triggerDriversQueryIfStatusPendingOrError())
  })

  useEffect(() => {
    onMount()
  }, [])

  const dataObject = useMemo(
    () => ({
      activeAndInactiveDrivers,
      activeDrivers,
      driversOptionsMeta,
      driverGroupsMapById,
      allDriversById,
    }),
    [
      activeAndInactiveDrivers,
      activeDrivers,
      driversOptionsMeta,
      driverGroupsMapById,
      allDriversById,
    ],
  )

  const selectedData = useMemo(
    () => (select ? select(dataObject) : (dataObject as SelectedData)),
    [dataObject, select],
  )

  return useMemo((): UseDriversQueryReturn<SelectedData> => {
    const fetchStatus = driversQuery.fetchStatus
    if (driversQuery.status === 'pending') {
      return { status: 'pending', fetchStatus, data: undefined }
    }
    if (driversQuery.status === 'error') {
      // Data can still be there, just like react-query
      return { status: 'error', fetchStatus, data: selectedData }
    }
    return { status: 'success', fetchStatus, data: selectedData }
  }, [driversQuery, selectedData])
}

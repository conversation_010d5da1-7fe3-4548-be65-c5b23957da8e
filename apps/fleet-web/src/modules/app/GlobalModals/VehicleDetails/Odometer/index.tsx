import { useEffect, useMemo, useState } from 'react'
import { size } from 'lodash'
import { zodResolver } from '@hookform/resolvers/zod'
import {
  Button,
  CircularProgress,
  colors,
  DataGrid,
  LinearProgress,
  Stack,
  Typography,
  type GridColDef,
} from '@karoo-ui/core'
import { TextFieldControlled } from '@karoo-ui/core-rhf'
import CreateOutlinedIcon from '@mui/icons-material/CreateOutlined'
import WarningAmberOutlinedIcon from '@mui/icons-material/WarningAmberOutlined'
import { DateTime } from 'luxon'
import { useForm, useWatch } from 'react-hook-form'
import { match } from 'ts-pattern'
import { z } from 'zod'

import type { VehicleId } from 'api/types'
import { getSettings_UNSAFE } from 'duxs/user-sensitive-selectors'
import { getVehiclesById } from 'duxs/vehicles'
import {
  UserDataGridWithSavedSettingsOnIDB,
  useUserFormatLengthInKmOrMiles,
} from 'src/modules/components/connected'
import { useVehicleOdometerQuery } from 'src/modules/detail/components/vehicle/Odometer/api/useVehicleOdometerQuery'
import {
  useVehicleOdometerUpdateHistoryQuery,
  type FetchVehicleOdometerUpdateHistory,
} from 'src/modules/detail/components/vehicle/Odometer/api/useVehicleOdometerUpdateHistoryQuery'
import { useTypedSelector } from 'src/redux-hooks'

import { ctIntl } from 'cartrack-ui-kit'
import OdometerUpdateButton from './OdometerUpdateButton'

const validationSchema = z.object({
  odometer: z.number(),
})

const Odometer = ({ vehicleId }: { vehicleId: VehicleId }) => {
  const { distanceInMiles } = useTypedSelector(getSettings_UNSAFE)
  const { formatLengthInKmOrMiles } = useUserFormatLengthInKmOrMiles()

  const [isEditingOdometer, setIsEditingOdometer] = useState(false)

  const fullVehicleData = useTypedSelector(getVehiclesById).get(vehicleId)

  const vehicleOdometerQuery = useVehicleOdometerQuery({ vehicleId })
  const vehicleOdometerUpdateHistoryQuery = useVehicleOdometerUpdateHistoryQuery({
    vehicleId: vehicleId,
  })

  const defaultFormValues = {
    odometer: 0,
  }

  const {
    control,
    reset,
    setValue: setFormValue,
  } = useForm<z.infer<typeof validationSchema>>({
    resolver: zodResolver(validationSchema),
    mode: 'all',
    defaultValues: defaultFormValues,
  })

  const watchedOdometerValue = useWatch({ name: 'odometer', control })

  useEffect(() => {
    if (vehicleOdometerQuery.status === 'success' && vehicleOdometerQuery.data) {
      reset({ odometer: vehicleOdometerQuery.data.odometer })
    }
  }, [
    reset,
    vehicleOdometerQuery.data,
    vehicleOdometerQuery.data?.odometer,
    vehicleOdometerQuery.status,
  ])

  const columns = useMemo(
    (): Array<GridColDef<FetchVehicleOdometerUpdateHistory.Return[number]>> => [
      {
        field: 'updatedDate',
        headerName: ctIntl.formatMessage({ id: 'Date' }),
        valueGetter: (_, row) =>
          DateTime.fromJSDate(new Date(row.updatedDate)).toLocaleString(
            DateTime.DATE_SHORT,
          ),
      },
      {
        field: 'updatedValue',
        headerName: ctIntl.formatMessage({ id: 'Update' }),
        valueGetter: (_, row) =>
          ctIntl.formatMessage(
            { id: 'vehicleDetails.odometer.updateHistory.updateColumn' },
            {
              values: {
                previousOdometerValue: row.previousValue,
                nextOdometerValue: row.updatedValue,
                distanceUnit: distanceInMiles ? 'mi' : 'km',
              },
            },
          ),
        flex: 1,
      },
      {
        field: 'updatedBy',
        headerName: ctIntl.formatMessage({ id: 'Updated By' }),
        valueGetter: (_, row) => row.updatedBy,
      },
    ],
    [distanceInMiles],
  )

  return (
    <Stack
      direction={'column'}
      gap={2}
      sx={{ overflowY: 'auto' }}
    >
      <Stack
        gap={2}
        direction={'column'}
        sx={{
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: 'white',
          border: '1px solid #0000001F',
          borderRadius: '4px',
          p: 2,
        }}
      >
        <Typography
          color={'secondary'}
          sx={{ textTransform: 'uppercase' }}
        >
          {ctIntl.formatMessage({ id: 'Total Odometer' })}
        </Typography>
        <Stack
          direction={'row'}
          gap={1}
          sx={{ alignItems: 'baseline' }}
        >
          <Typography
            variant="h5"
            component="span"
          >
            {formatLengthInKmOrMiles({
              valueInKm: vehicleOdometerQuery.data?.odometer || 0,
              intlFormatNumberOptions: {
                maximumFractionDigits: 0,
                style: undefined,
              },
            })}
          </Typography>
          <Typography
            variant="subtitle2"
            component="span"
          >
            {distanceInMiles ? 'mi' : 'km'}
          </Typography>
          <Typography
            sx={{ color: colors.orange[500] }}
            variant="caption"
            component="span"
          >
            {`(${size(vehicleOdometerUpdateHistoryQuery.data)} ${ctIntl.formatMessage(
              {
                id: 'odometer.updates',
              },
              { values: { count: size(vehicleOdometerUpdateHistoryQuery.data) } },
            )})`.toLowerCase()}
          </Typography>
        </Stack>
      </Stack>
      {match(vehicleOdometerQuery)
        .with({ status: 'pending' }, () => (
          <Stack sx={{ alignItems: 'center', overflowY: 'hidden' }}>
            <CircularProgress size={'40px'} />
          </Stack>
        ))
        .with({ status: 'error' }, () => (
          <Stack
            sx={{
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Typography>
              {ctIntl.formatMessage({
                id: 'Something went wrong, please try again',
              })}
            </Typography>
          </Stack>
        ))
        .with({ status: 'success' }, () =>
          isEditingOdometer && fullVehicleData ? (
            <Stack
              direction={'column'}
              gap={2}
              sx={{
                backgroundColor: 'white',
                border: '1px solid #FF9800',
                borderRadius: '4px',
                p: 2,
              }}
            >
              <Stack
                direction={'row'}
                gap={1}
              >
                <WarningAmberOutlinedIcon
                  fontSize="small"
                  sx={{ color: '#FF9800' }}
                />
                <Typography
                  sx={{ color: '#663C00' }}
                  variant="subtitle2"
                >
                  {ctIntl.formatMessage({
                    id: 'Please be aware that this update is irreversible and will impact odometer data throughout the platform.',
                  })}
                </Typography>
              </Stack>
              <Stack
                direction={'row'}
                gap={1}
                sx={{ alignItems: 'center' }}
              >
                <TextFieldControlled
                  required
                  inputProps={{ inputMode: 'numeric' }}
                  sx={{ flexGrow: 1 }}
                  ControllerProps={{
                    control,
                    name: 'odometer',
                  }}
                  label={`${ctIntl.formatMessage({ id: 'New Odometer' })} (${
                    distanceInMiles ? 'mi' : 'km'
                  })`}
                  onChange={({ target: { value } }) => {
                    const trimmedValue = value.replaceAll(/\s/g, '')

                    // Accept 7 digits at most
                    // eslint-disable-next-line sonarjs/concise-regex
                    if (/^[0-9]{0,7}$/.test(trimmedValue)) {
                      const numberOdometer = Number(trimmedValue)

                      if (numberOdometer >= 0) {
                        setFormValue('odometer', numberOdometer, {
                          shouldValidate: false,
                        })
                      }
                    }
                  }}
                />
                <Button
                  onClick={() => {
                    setIsEditingOdometer(false)
                  }}
                  color="secondary"
                  variant="outlined"
                  size="small"
                >
                  {ctIntl.formatMessage({ id: 'Cancel' })}
                </Button>
                <OdometerUpdateButton
                  odometerValue={watchedOdometerValue}
                  vehicleId={vehicleId}
                  vehicleRegistration={fullVehicleData.registration}
                  closeOdometerEdition={() => setIsEditingOdometer(false)}
                />
              </Stack>
            </Stack>
          ) : (
            <Stack
              direction={'row'}
              gap={1}
              sx={{
                alignItems: 'center',
                backgroundColor: 'white',
                border: '1px solid #FF9800',
                borderRadius: '4px',
                p: 2,
              }}
            >
              <WarningAmberOutlinedIcon
                fontSize="small"
                sx={{ color: '#FF9800' }}
              />
              <Typography
                color={'secondary'}
                variant={'caption'}
              >
                {ctIntl.formatMessage({
                  id: 'If there is some inconsistency with the real value, you can manually',
                })}
              </Typography>
              <Typography
                onClick={() => setIsEditingOdometer(true)}
                sx={{
                  color: '#2196F3',
                  '&:hover': {
                    cursor: 'pointer',
                    textDecoration: 'underline',
                    textDecorationColor: '#2196F3',
                  },
                }}
                variant={'caption'}
              >
                {ctIntl.formatMessage({
                  id: 'update the odometer value',
                })}
              </Typography>
              <CreateOutlinedIcon
                fontSize="small"
                sx={{ color: '#2196F3' }}
              />
            </Stack>
          ),
        )
        .exhaustive()}
      {Boolean(size(vehicleOdometerUpdateHistoryQuery.data)) && (
        <>
          <Typography
            variant="subtitle2"
            component={'span'}
          >
            {ctIntl.formatMessage({
              id: 'vehicleDetails.odometer.updateHistory.title',
            })}
          </Typography>

          {match(vehicleOdometerUpdateHistoryQuery)
            .with({ status: 'error' }, () => null)
            .with({ status: 'pending' }, () => (
              <Stack sx={{ alignItems: 'center', overflowY: 'hidden' }}>
                <CircularProgress size={'40px'} />
              </Stack>
            ))
            .with({ status: 'success' }, ({ data: updates }) => (
              <UserDataGridWithSavedSettingsOnIDB
                Component={DataGrid}
                dataGridId="GlobalModal-VehicleDetails-Odometer"
                data-testid="GlobalModal-VehicleDetails-Odometer"
                disableRowSelectionOnClick
                columns={columns}
                rows={updates}
                pagination
                pageSizeOptions={[25, 50, 100]}
                getRowId={(row) => row.id}
                slots={{
                  loadingOverlay: LinearProgress,
                }}
                initialState={{
                  pagination: {
                    paginationModel: { pageSize: 25, page: 0 },
                  },
                }}
                slotProps={{
                  pagination: {
                    material: { showFirstButton: true, showLastButton: true },
                  },
                }}
              />
            ))
            .exhaustive()}
        </>
      )}
    </Stack>
  )
}

export default Odometer

import * as R from 'remeda'
import { match } from 'ts-pattern'
import { z } from 'zod'

import { PermissionLookup, type PermissionLookupKeyType } from 'api/admin'
import {
  companyDepartmentIdsSchema,
  vehicleIdSchema,
  type DriverGroupId,
  type DriverId,
} from 'api/types'
import type { FixMeAny, PromiseResolvedType } from 'src/types'
import { safeParseFromZodSchema } from 'src/util-functions/zod-utils'

import {
  groupByAndMapValues,
  isNilOrTrimmedEmptyString,
  toImmutable,
  utcTimestampToMs,
} from 'cartrack-utils'
import apiCaller, { apiCallerNoX } from '../api-caller'
import type { Ct_fleet_get_drivers_with_eld } from './types'

const DriverLicenseTypeSchema = z.object({
  license_type_id: z.string().nullable(),
  license_type: z.string().nullable(),
  expiration_date: z.string().nullable(),
  license_number: z.string().nullable(),
})

type DriverLicenseType = z.infer<typeof DriverLicenseTypeSchema>

const parseDriverLicenseTypes = (input: string): Array<DriverLicenseType> => {
  const trimmed = input.trim().slice(1, -1) // Remove the surrounding braces

  const items = trimmed.match(/"([^"]+)"|([^,]+)/g)
  if (!items) {
    return []
  }

  const parsedItems = items.map((item) => {
    // eslint-disable-next-line sonarjs/anchor-precedence
    const cleanItem = item.replaceAll(/^"|"$/g, '')
    const [license_type_id, license_type, expiration_date, license_number] = cleanItem
      .split(';')
      .map((part) => (part === 'NULL' ? null : part))
    return { license_type_id, license_type, expiration_date, license_number }
  })

  const parsingValue = DriverLicenseTypeSchema.array().safeParse(parsedItems)
  return parsingValue.success ? parsingValue.data : []
}

export function parseDrivers(
  rawDrivers: Ct_fleet_get_drivers_with_eld.ApiOutput['ct_fleet_get_drivers_with_eld'],
  rawDriverGroups: Ct_fleet_get_drivers_with_eld.ApiOutput['ct_fleet_get_driverlist_dgroup'],
  rawGroupMap: Ct_fleet_get_drivers_with_eld.ApiOutput['ct_fleet_get_driverlist_dgroupmap'],
) {
  const drivers = rawDrivers.map((d) => {
    let statusClassName =
      (d.out_eld_status && d.out_eld_status.toLowerCase().replace(' ', '-')) ||
      'no-signal'

    if (statusClassName === 'on-duty-not driving') statusClassName = 'on-duty'
    if (statusClassName === 'unknown') statusClassName = 'off-duty'

    return {
      id: d.out_driver_id as DriverId,
      name:
        d.out_driver_name + (d.out_driver_surname ? ' ' + d.out_driver_surname : ''),
      mainOfficeAddress: d.out_main_office_address || '',
      description: d.out_driver_description,
      gender:
        // eslint-disable-next-line no-nested-ternary
        Number(d.out_gender) === 1
          ? ('M' as const)
          : Number(d.out_gender) === 2
            ? ('F' as const)
            : ('' as const),
      rating: Number(d.out_driver_behaviour_score),
      vehicleRegistration: d.out_vehicle_registration || '',
      vehicleType: d.out_vehicle_type_id || '0',
      active: d.out_driver_statuses.includes('10'),
      statusClassName,
      seenToday: d.out_eld_status_time
        ? !d.out_eld_status_time.includes('days')
        : false,
      totalShiftHours: d.out_total_shift_hours,
      departmentIds: safeParseFromZodSchema(
        companyDepartmentIdsSchema,
        d.out_department_ids,
        {
          defaultValue: () => null,
        },
      ),
      driverLoginUsername: d.driver_login_username,
      cellPhone: d.out_cell_phone,
      email: d.out_driver_email,
      licenseNumber: d.out_license_number,
      licenseExpirationTime: d.out_license_expiration_timestamp
        ? utcTimestampToMs(d.out_license_expiration_timestamp)
        : null,
      licenseCode: d.out_license_code,
      licenseTypes: parseDriverLicenseTypes(d.out_driver_license_types),
      idPassportNumber:
        typeof d.out_id_passport_number === 'string' ? d.out_id_passport_number : null,
      employeeNumber: d.out_employee_number,

      vehicleId: isNilOrTrimmedEmptyString(d.out_vehicle_id)
        ? null
        : vehicleIdSchema.safeParse(d.out_vehicle_id).data ?? null,

      // Defects
      newDefectsCount: Number(d.out_new_defects) || 0,
      repairedDefectsCount: Number(d.out_repaired_defects) || 0,
      inspectedDefectsCount: Number(d.out_inspected_defects) || 0,
      permissions: d.out_permissions,
      ownerUsername: d.out_driver_owner,
    }
  })

  const driverIdsByGroupId = groupByAndMapValues(
    rawGroupMap,
    'group_driver_id',
    'client_driver_id',
  )

  const groups = rawDriverGroups.map((raw) => {
    // convert the number to the readable permission
    const permissionId = match<
      typeof raw.permission_id,
      PermissionLookupKeyType | 'hide'
    >(raw.permission_id)
      .with(
        '1',
        '2',
        '3',
        '4',
        '5',
        '6',
        '7',
        (value) =>
          R.keys(PermissionLookup).find(
            (key) => PermissionLookup[key] === Number(value),
          ) ?? 'hide',
      )
      .otherwise(() => 'hide') // if the value is invalid, set it as 'hide' default

    return {
      id: raw.group_driver_id as DriverGroupId,
      group_driver_id: raw.group_driver_id as DriverGroupId,
      name: raw.name,
      description: raw.description,
      clientUserId: raw.client_user_id,
      permissionId: permissionId,
      itemIds: (driverIdsByGroupId[raw.group_driver_id] || []) as Array<string>,
    }
  })

  return toImmutable({
    drivers,
    groups,
  })
}

function normalizeGroup(g: Record<string, any>) {
  return {
    group_driver_id: g.id,
    name: g.name,
  }
}

function parseEventLogSignoff(result: Record<string, any>) {
  const r = result.fetch_event_log_signoff_by_day[0]
  if (!r) return null

  const { client_trailer_id: ct = [], client_trailer_id_2: ct2 = [] } = r
  const trailers = []
  if (ct && ct !== 0) trailers.push(ct)
  if (ct2 && ct2 !== 0) trailers.push(ct2)

  return {
    isSigned: true,
    date: r.signoff_date,
    distance: Math.round(Number(r.distance) / 200 / 1.60934),
    driverId: r.client_driver_id,
    coDriverId: r.co_client_driver_id,
    vehicleId: r.vehicle_id,
    signatureImage: r.signature_path,
    shippingDocDescription: r.shipment_doc,
    trailers,
  }
}

const driversAPI = {
  updateDriverGroup(group: Record<string, any>) {
    return apiCaller('ct_fleet_rename_client_group_name', {
      groupData: normalizeGroup(group),
      type: 'driver',
    })
  },

  updateDriverGroupItems(groupId: FixMeAny, itemIds: FixMeAny) {
    return apiCaller('ct_fleet_client_save_driver_group', {
      groupDriverId: groupId,
      DriverIds: itemIds,
    })
  },

  deleteDriverGroup(groupId: FixMeAny) {
    return apiCallerNoX('ct_fleet_delete_client_group_vehicle_driver', {
      type: 'driver',
      groupVehicleDriverIds: [groupId],
    })
  },

  createDriverGroup(name: FixMeAny) {
    return apiCaller('ct_fleet_create_client_vehicle_driver_group', {
      vehicleGroupData: {
        name,
        group_driver_id: '',
        group_vehicle_id: '',
        group_geofence_id: '',
        group_status_id: '',
        description: '',
      },
      type: 'driver',
    }).then((res: FixMeAny) => res.groupData[0].group_driver_id)
  },

  fetchDrivers() {
    return apiCaller('ct_fleet_get_drivers_with_eld').then(
      (res: Ct_fleet_get_drivers_with_eld.ApiOutput) =>
        parseDrivers(
          res.ct_fleet_get_drivers_with_eld,
          res.ct_fleet_get_driverlist_dgroup,
          res.ct_fleet_get_driverlist_dgroupmap,
        ),
    )
  },

  fetchEventLogSignoff(
    driverId: FixMeAny,
    dateObj: { day: string; month: string; year: string },
  ) {
    const date = `${dateObj.year}-${dateObj.month}-${dateObj.day}`
    return apiCaller(
      'fetch_event_log_signoff_by_day',
      { driverId, date },
      { noX: true },
    ).then(parseEventLogSignoff)
  },
}

export default driversAPI

export type FetchDriversResponse = PromiseResolvedType<typeof driversAPI.fetchDrivers>

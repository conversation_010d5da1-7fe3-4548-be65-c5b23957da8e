import { createAction, createSelector } from '@reduxjs/toolkit'
import * as R from 'remeda'

import {
  reconciliateGroupItemIds,
  makeHash,
  insensitiveCompare,
  toImmutable,
  anomonymizeIdPassport,
} from 'cartrack-utils'
import { uniqListByColumn } from 'duxs/shared'
import type { FixMeAny } from 'src/types'
import type { AppState } from 'src/root-reducer'
import { createStatefulAction } from './utils'
import type { FetchDriversResponse } from '../api/drivers'
import { createSelectorWithStrictMode } from 'src/redux-utils'
import type { DriverGroupId, DriverId, VehicleId } from 'api/types'
import type { NonEmptyMutableArray, NormalizedList } from 'src/types/utils'

// Actions
export const UPDATE_DRIVER_GROUP = 'UPDATE_DRIVER_GROUP'
export const DELETE_DRIVER_GROUP = 'DELETE_DRIVER_GROUP'
export const CREATE_DRIVER_GROUP = 'CREATE_DRIVER_GROUP'
export const ON_CREATE_DRIVER_GROUP = 'ON_CREATE_DRIVER_GROUP'

export const FETCH_LOG_SIGNOFF = 'FETCH_LOG_SIGNOFF'
export const RECEIVE_LOG_SIGNOFF = 'RECEIVE_LOG_SIGNOFF'
export const invalidateDriversList = createAction<void>('INVALIDATE_DRIVERS_LIST')
export const fetchDrivers =
  createStatefulAction<Pick<FetchDriversResponse, 'drivers' | 'groups'>>(
    '/fetchDrivers',
  )

export const triggerDriversQueryIfStatusPendingOrError = createAction<void>(
  'TRIGGER_DRIVERS_QUERY_IF_STATUS_PENDING_OR_ERROR',
)

export type State = {
  hasLoaded: boolean
  /** Similar to react-query statuses */
  driversQuery: {
    fetchStatus: 'fetching' | 'idle'
    status: 'pending' | 'error' | 'success'
  }
  drivers: FetchDriversResponse['drivers']
  groups: FetchDriversResponse['groups']
  currentSignoff: FixMeAny
  isCreatingDriverGroup: boolean
}

// Reducer
export const initialState: State = {
  hasLoaded: false,
  driversQuery: {
    fetchStatus: 'idle',
    status: 'pending',
  },
  drivers: [],
  groups: [],
  currentSignoff: undefined,
  isCreatingDriverGroup: false,
}

export default function reducer(state = initialState, action: FixMeAny): State {
  if (fetchDrivers.processing.match(action)) {
    return {
      ...state,
      driversQuery: {
        fetchStatus: 'fetching',
        // Keep data status intact when fetching (like react query does)
        status: state.driversQuery.status,
      },
    }
  } else if (fetchDrivers.succeeded.match(action)) {
    return {
      ...state,
      drivers: action.payload.drivers,
      groups: action.payload.groups,
      driversQuery: {
        fetchStatus: 'idle',
        status: 'success',
      },
      hasLoaded: true,
    }
  } else if (fetchDrivers.failed.match(action)) {
    return {
      ...state,
      driversQuery: {
        fetchStatus: 'idle',
        status: 'error',
      },
    }
  }

  switch (action.type) {
    case RECEIVE_LOG_SIGNOFF: {
      return {
        ...state,
        currentSignoff: action.payload.signoff,
      }
    }
    case CREATE_DRIVER_GROUP: {
      return {
        ...state,
        isCreatingDriverGroup: true,
      }
    }
    case ON_CREATE_DRIVER_GROUP: {
      return {
        ...state,
        isCreatingDriverGroup: false,
      }
    }
    default: {
      return state
    }
  }
}

// Action Creators
export function updateDriverGroup(group: FixMeAny, selectedIds: FixMeAny) {
  return {
    type: UPDATE_DRIVER_GROUP,
    payload: { group, selectedIds },
  }
}

export function deleteDriverGroup(groupId: FixMeAny, groupName: FixMeAny) {
  return {
    type: DELETE_DRIVER_GROUP,
    payload: { groupId, groupName },
  }
}

export function createDriverGroup(name: string) {
  return {
    type: CREATE_DRIVER_GROUP,
    payload: {
      name,
    },
  }
}

export function fetchLogSignoff(
  driverId: FixMeAny,
  dateObj: { day: string; month: string; year: string },
) {
  return {
    type: FETCH_LOG_SIGNOFF,
    payload: { driverId, dateObj },
  }
}

// Selectors
export const getHasLoaded = (state: AppState) => state.drivers.hasLoaded
export const getDrivers = createSelector(
  (state: AppState) => state.drivers.drivers,
  (drivers) => [...drivers].sort((a, b) => insensitiveCompare(a.name, b.name)),
)

export const getDriversById = createSelector(getDrivers, (drivers) => {
  const driversById = new Map<DriverId, (typeof drivers)[number]>()
  for (const driver of drivers) {
    driversById.set(driver.id, driver)
  }
  return driversById
})

export const getDriversQuery = (state: AppState) => state.drivers.driversQuery

export const getVehicleIdDriversMap = createSelectorWithStrictMode(
  getDrivers,
  (drivers) => {
    const vehicleIdDriversMap = new Map<
      VehicleId,
      NonEmptyMutableArray<(typeof drivers)[number]>
    >()

    for (const driver of drivers) {
      if (R.isNullish(driver.vehicleId)) {
        continue
      }

      const currentArray = vehicleIdDriversMap.get(driver.vehicleId)
      if (!currentArray) {
        vehicleIdDriversMap.set(driver.vehicleId, [driver])
        continue
      }

      currentArray.push(driver)
    }

    return toImmutable(vehicleIdDriversMap)
  },
)

export const getDistinctUnionFromDriversListWithActiveDrivers = createSelector(
  (state: AppState) => (R.isArray(state.drivers.drivers) ? state.drivers.drivers : []),
  (_: AppState, driversList: FetchDriversResponse['drivers']) =>
    R.isArray(driversList) ? driversList : [],
  (allDrivers, driversList) =>
    toImmutable(
      allDrivers
        .reduce<Array<(typeof allDrivers)[number]>>((acc, driver) => {
          if (
            (driver && driver.active) ||
            driversList.some((item) => item && item.id === driver.id)
          ) {
            acc.push(driver)
          }

          return acc
        }, [])
        .sort((a, b) => insensitiveCompare(a.name, b.name)),
    ),
)

export const getActiveDrivers = createSelector(getDrivers, (drivers) =>
  drivers.filter((d) => d.active),
)
export const getDriversMap = createSelector(getDrivers, (drivers) =>
  drivers.reduce(
    (acc, d) => {
      acc[d.id] = d
      return acc
    },
    {} as Record<string, (typeof drivers)[number]>,
  ),
)

type DriverOptionMetaOption = {
  label: string
  value: DriverId
} & FetchDriversResponse['drivers'][number]

export type DriversOptionsMeta = {
  activeDriversMeta: NormalizedList<DriverOptionMetaOption>
}

export const getDriversOptionsMeta = createSelector(
  getDrivers,
  (drivers): DriversOptionsMeta => {
    const meta: DriversOptionsMeta = {
      activeDriversMeta: {
        array: [],
        byId: new Map(),
      },
    }
    for (const driver of drivers) {
      if (!driver.active) {
        continue
      }
      const option: DriverOptionMetaOption = {
        label: driver.name,
        value: driver.id,
        ...driver,
      }
      meta.activeDriversMeta.array.push(option)
      meta.activeDriversMeta.byId.set(option.id, option)
    }
    meta.activeDriversMeta.array.sort((a, b) => a.label.localeCompare(b.label))

    return meta
  },
)

export const getDriverGroups = (state: AppState) => state.drivers.groups

export const getDriverGroupsMapById = createSelector(getDriverGroups, (groups) => {
  const groupsMap = new Map<DriverGroupId, (typeof groups)[number]>()
  for (const group of groups) {
    groupsMap.set(group.id, group)
  }
  return groupsMap as Readonly<typeof groupsMap>
})

export const getIsCreatingDriversGroup = (state: AppState) =>
  state.drivers.isCreatingDriverGroup

export const getActiveDriverOptions = createSelector(getDrivers, (drivers) =>
  drivers
    .filter((d) => d.active)
    .map(({ name, id, idPassportNumber: idNumber }) => {
      const label = `${name}${
        idNumber ? ' (' + anomonymizeIdPassport(idNumber) + ')' : ''
      }`

      return { label, name, value: id, key: id }
    })
    .sort((a, b) => insensitiveCompare(a.name, b.name)),
)

export const getDriver = createSelector(
  getDrivers,
  (_: AppState, id: string) => id,
  (drivers, id) => drivers.find((driver) => driver.id === id),
)

export const getDriversById_DO_NOT_USE = createSelector(
  getDrivers,
  (_: AppState, ids: Array<FixMeAny>) => ids,
  (drivers, ids) => {
    const hash = makeHash(ids)
    return drivers.filter((v) => v.id in hash)
  },
)

export const getDriverGroup = createSelector(
  getDriverGroups,
  (_: AppState, groupId: string) => groupId,
  getDrivers,
  (groups, groupId, drivers) => {
    const group = groups.find((group) => group.id === groupId)
    return reconciliateGroupItemIds(group, drivers)
  },
)

export const getCurrentSignoff = (state: AppState) => state.drivers.currentSignoff

export const getUniqDriverOptions = createSelector(getActiveDrivers, (drivers) =>
  uniqListByColumn(drivers),
)

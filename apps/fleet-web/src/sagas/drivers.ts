import { push, goBack } from 'connected-react-router'
import { takeLatest, fork, call, put, takeLeading, select } from 'typed-redux-saga'

import driversAPI from '../api/drivers'
import { poll, makeLoadable, makeToast, makeConfirmation } from './utils'
import { ctIntl } from 'src/util-components/ctIntl'
import {
  // Driver groups
  CREATE_DRIVER_GROUP,
  ON_CREATE_DRIVER_GROUP,
  UPDATE_DRIVER_GROUP,
  DELETE_DRIVER_GROUP,
  FETCH_LOG_SIGNOFF,
  RECEIVE_LOG_SIGNOFF,
  type fetchLogSignoff,
  type deleteDriverGroup,
  type updateDriverGroup,
  type createDriverGroup,
  fetchDrivers,
  invalidateDriversList,
  triggerDriversQueryIfStatusPendingOrError,
  getDriversQuery,
} from 'duxs/drivers'
import { actions } from 'duxs/drivers/driver-details'
import { LIST } from 'src/modules/app/components/routes/list'
import { generatePath } from 'react-router'
import { invalidateQueriesOnEntitiesMutationFromSaga } from './saga-invalidation-utils'
import { waitForUrlToNotContainLoginSearchParams } from 'src/AppRootEffects'

const { onCreateDriver, onUpdateDriverDetails } = actions

function* pollDrivers() {
  yield* call(fetchDriversSaga)
}

export function* fetchDriversSaga() {
  yield* waitForUrlToNotContainLoginSearchParams()

  try {
    yield* put(fetchDrivers.processing())
    const { drivers, groups } = yield* call(driversAPI.fetchDrivers)

    yield* put(fetchDrivers.succeeded({ drivers, groups }))
  } catch (error) {
    yield* put(fetchDrivers.failed())
    throw error
  }
}

function* createDriverGroupSaga({ payload }: ReturnType<typeof createDriverGroup>) {
  try {
    const groupId = yield* call(driversAPI.createDriverGroup, payload.name)

    yield* call(fetchDriversSaga)
    yield* put(
      push({
        pathname: generatePath(LIST.subMenusRoutes.DRIVERS.subPaths.GROUP, { groupId }),
        state: { defaultEdit: true },
      }),
    )
    yield* fork(
      makeToast,
      'success',
      ctIntl.formatMessage(
        { id: '{name} was successfully created' },
        { values: { name: payload.name } },
      ),
    )

    yield invalidateQueriesOnEntitiesMutationFromSaga({ entities: ['driver'] })
  } catch (error) {
    yield* fork(makeToast, 'error', error.message)
  }

  yield* put({ type: ON_CREATE_DRIVER_GROUP })
}

function* updateDriverGroupSaga({
  payload: { group, selectedIds },
}: ReturnType<typeof updateDriverGroup>) {
  yield* call(driversAPI.updateDriverGroup, group)
  yield* call(driversAPI.updateDriverGroupItems, group.id, selectedIds)
  yield* call(fetchDriversSaga)
  yield* fork(
    makeToast,
    'success',
    ctIntl.formatMessage(
      { id: '{name} was successfully updated' },
      { values: { name: group.name } },
    ),
  )

  yield invalidateQueriesOnEntitiesMutationFromSaga({ entities: ['driver'] })
}

function* deleteDriverGroupSaga({
  payload: { groupId, groupName },
}: ReturnType<typeof deleteDriverGroup>) {
  if (
    yield* call(
      makeConfirmation,
      ctIntl.formatMessage(
        {
          id: 'group.delete.confirmation',
          defaultMessage: 'Are you sure you wish to delete this group?',
        },
        { values: { name: groupName } },
      ),
      { yes: 'Delete', no: 'Cancel' },
    )
  ) {
    yield* call(driversAPI.deleteDriverGroup, groupId)
    yield* call(fetchDriversSaga)
    yield* put(goBack())
    yield* call(
      makeToast,
      'success',
      `${groupName ? `"${groupName}"` : 'Group'}` +
        ' ' +
        ctIntl.formatMessage({ id: 'was successfully deleted.' }),
    )

    yield invalidateQueriesOnEntitiesMutationFromSaga({ entities: ['driver'] })
  }
}

export function* updateDriversList() {
  yield* call(fetchDriversSaga)
}

function* triggerDriversQueryIfStatusPendingOrErrorSaga() {
  const driversQuery = yield* select(getDriversQuery)
  if (driversQuery.status === 'pending' || driversQuery.status === 'error') {
    // Only fetch if we have never fetched before or if the last time it errored.
    // We have polling to keep it up to date.
    yield* call(fetchDriversSaga)
  }
}

function* fetchLogSignoffSaga({
  payload: { driverId, dateObj },
}: ReturnType<typeof fetchLogSignoff>) {
  const signoff = yield* call(driversAPI.fetchEventLogSignoff, driverId, dateObj)
  yield* put({ type: RECEIVE_LOG_SIGNOFF, payload: { signoff } })
}

export default function* driversSaga() {
  yield* fork(poll, fetchDrivers.succeeded, pollDrivers, 120000, () => ({
    maxTimes: 1,
  }))
  yield* takeLatest(CREATE_DRIVER_GROUP, makeLoadable, createDriverGroupSaga)
  yield* takeLatest(UPDATE_DRIVER_GROUP, makeLoadable, updateDriverGroupSaga)
  yield* takeLatest(DELETE_DRIVER_GROUP, makeLoadable, deleteDriverGroupSaga)

  // Update drivers list on...
  yield* takeLatest(
    [onCreateDriver, onUpdateDriverDetails, invalidateDriversList],
    updateDriversList,
  )
  yield* takeLeading(
    triggerDriversQueryIfStatusPendingOrError,
    triggerDriversQueryIfStatusPendingOrErrorSaga,
  )

  yield* takeLatest(FETCH_LOG_SIGNOFF, fetchLogSignoffSaga)
}

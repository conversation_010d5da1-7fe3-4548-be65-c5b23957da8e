import { spawn, call, all, putResolve, delay } from 'typed-redux-saga'
import { MIFLEET_API_ERROR_TYPES } from '../api/mifleet/api-caller-constants'
import driversSaga from './drivers'
import geofencesSaga from './geofences'
import landmarksSaga from './landmarks'
import liveVisionSaga from './live-vision'
import userSaga from './user'
import userRolesSaga from './mifleet/user-roles'
import vehiclesSaga from './vehicles'
import type * as H from 'history'

// Alerts
import alertSettingsSaga from './alert-settings'
import costsAlertsSaga from './mifleet/alerts/alerts'
import costsActiveAlertsSaga from './mifleet/alerts/active-alerts'
import trafficAlertsSaga from './traffic-alerts'

import adminSaga from './admin'
import reportsSaga from './reports'
import dvirsSaga from './dvirs'
import documentsSaga from './documents'
import driverIdTagsSaga from './driver-id-tags'
import mapSaga from './map'
import navigationSaga from './navigation'
import importerSaga from './importer'
import minitrackerSaga from './mini-tracker'
import listDataSaga from './mifleet/list-data'
import overviewSaga from './mifleet/overview/overview'
import suppliersSaga from './mifleet/suppliers'
import costCentresSaga from './mifleet/cost-centres'
import costCentresGroupsSaga from './mifleet/cost-centres-groups'
import reportProfileSaga from './report-profiles'
import mifleetDocumentsSaga from './mifleet/documents'
import regulatorySaga from './mifleet/regulatory'
import operationalSaga from './mifleet/operational'
import vehicleCostsSaga from './mifleet/vehicle-costs'
import drvCostsView from './mifleet/drivers/driver-costs'
import drvUpdateView from './mifleet/drivers/driver-costs-form'
import drvPermitsView from './mifleet/drivers/driver-permits'
import driverStaffPositions from './mifleet/drivers/driver-staff-positions'
import driverLeaveSaga from './mifleet/drivers/driver-leave'
import assignVehiclesSaga from './mifleet/drivers/assign-vehicles'
import trailersSaga from './trailers'
import taxesSaga from './taxes'
import fiscalSaga from './fiscal'
import messagingSaga from './messaging'
import engineSaga from './engine'
import routesSaga from './routes'
import exportSaga from './export'
import surveySaga from './survey'
import { editUserSaga } from 'src/modules/admin/manage-users/EditUser/combined-slice'
import vehicleMappingSaga from './vehicle-mapping'
// Privacy
import privacySaga from './privacy/privacy'

import { authenticationSaga } from 'src/modules/app/authentication/saga'
import { DetailsPanelSaga } from 'src/modules/map-view/FleetMapView/DetailsPanel/saga'

import { auditSaga } from 'src/modules/admin/audit/slice'

// Vehicle Popover
import { vehiclePopoverSaga } from 'src/modules/map-view/FleetMapView/LeftPanel/Vehicle/VehiclePopover/saga'

// Sisense
import { dashboardViewSaga } from 'src/modules/dashboard/saga'

import { makeToast } from './utils'
import timeline from './timeline'
import { logout } from 'duxs/user'
import type { Dispatch } from '@reduxjs/toolkit'
import type { FixMeAny } from 'src/types'
import { ctToast } from 'cartrack-ui-kit'

export type AppSagaInjectedDeps = {
  /** Useful to be able to dispatch on functions ran from sagas that are not generator functions themselves */
  storeDispatch: Dispatch
  history: H.History
}

function makeRestartable<
  Saga extends (...args: Array<any>) => Generator<any, void, any>,
>(saga: Saga, injectedDeps: AppSagaInjectedDeps) {
  return function* () {
    let delayAccumulator = 1
    const MAX_ACCUMULATE_VAL = 1000 // it will bring maximum 16.6 minute delay when delayAccumulator is multiplied by 1000 on Line: 154
    while (true) {
      try {
        yield* call(saga as FixMeAny, injectedDeps)
        throw new Error(
          `unexpected root saga termination. The root sagas are supposed to be sagas that live during the whole app lifetime! ${saga}`,
        )
      } catch (error) {
        if (error.name === MIFLEET_API_ERROR_TYPES.NOT_AUTHENTICATED) {
          // TODO - Show a snackbar saying that for technical reasons we failed to keep session on mifleet
          ctToast.fire(
            'error',
            'Mifleet session has been invalidated. Please re-login again to use Mifleet.',
          )
        } else {
          switch (error.message) {
            case 'Not logged in':
            case 'Login Failed':
            case 'Your session has been invalidated. Please log in again.': {
              yield* putResolve(logout({}))
              yield* call(makeToast, 'error', error.message)
              break
            }
            case 'SESSION_EXPIRED_DUE_TO_INACTIVITY': {
              yield* putResolve(logout({}))
              /* Increased duration is needed since the ADD_TOAST event may be triggered __before__ <ToastContainer />
                 is __actually__ available to render. Without this increase, we ran the risk of having the Toast not be shown at all. */
              yield* call(makeToast, 'error', error.message, 6000)
              break
            }
            default: {
              console.error('[Cartrack] - Saga uncaught error', error)
              yield* call(makeToast, 'error', error.message, undefined, {
                /* Since this is a non handled error, we do not have to worry about having ctIntl initialized and having translations */
                /* Thus, we skip strict validation */
                strict: false,
              })
            }
          }
          delayAccumulator = Math.min(delayAccumulator * 2, MAX_ACCUMULATE_VAL)
          yield* delay(delayAccumulator * 1000)

          console.info('Saga error, the saga will be restarted', error)
        }
      }
    }
  }
}

const makeRootSagas = (injectedDeps: AppSagaInjectedDeps) =>
  [
    auditSaga,
    driversSaga,
    landmarksSaga,
    userSaga,
    userRolesSaga,
    vehiclesSaga,
    vehiclePopoverSaga,
    // Alerts
    alertSettingsSaga,
    costsAlertsSaga,
    costsActiveAlertsSaga,
    trafficAlertsSaga,

    authenticationSaga,
    adminSaga,
    reportsSaga,
    dvirsSaga,
    documentsSaga,
    driverIdTagsSaga,
    mapSaga,
    navigationSaga,
    importerSaga,
    minitrackerSaga,
    listDataSaga,
    overviewSaga,
    suppliersSaga,
    costCentresSaga,
    costCentresGroupsSaga,
    reportProfileSaga,
    mifleetDocumentsSaga,
    regulatorySaga,
    operationalSaga,
    vehicleCostsSaga,
    drvCostsView,
    drvUpdateView,
    drvPermitsView,
    driverStaffPositions,
    driverLeaveSaga,
    assignVehiclesSaga,
    timeline,
    trailersSaga,
    taxesSaga,
    fiscalSaga,
    privacySaga,
    messagingSaga,
    engineSaga,
    routesSaga,
    vehicleMappingSaga,
    exportSaga,
    surveySaga,
    editUserSaga,
    // End of Communicator sagas
    dashboardViewSaga,
    DetailsPanelSaga,
  ]
    .filter(Boolean)
    .map((saga) => makeRestartable(saga, injectedDeps))

const makeBaseSagas = (injectedDeps: AppSagaInjectedDeps) =>
  [geofencesSaga, liveVisionSaga]
    .filter(Boolean)
    .map((saga) => makeRestartable(saga, injectedDeps))

// Combine sagas
export default function* rootSaga(injectedDeps: AppSagaInjectedDeps) {
  yield* all(
    [...makeRootSagas(injectedDeps), ...makeBaseSagas(injectedDeps)].map((saga) =>
      spawn(saga),
    ),
  )
}

export function* basicSaga(injectedDeps: AppSagaInjectedDeps) {
  yield* all(makeBaseSagas(injectedDeps).map((saga) => spawn(saga)))
}
